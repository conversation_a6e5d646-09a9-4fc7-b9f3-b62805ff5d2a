<template>
    <div v-if="topicId" class="hubble-page__content">
        <Guide v-model="showGuide"></Guide>
        <Document
            class="hubble-page__content-document"
            :topicId="topicId"
        ></Document>
        <ChatView :key="topicId" class="hubble-page__content-chat" :topicId="topicId"></ChatView>
    </div>
</template>
<script>
import ChatView from './chatView/index.vue';
import Document from './document/index.vue';
import Guide from '../components/guide/index.vue';
export default {
    components: {
        ChatView,
        Document,
        Guide,
    },
    data() {
        return {
            topicId: this.$route.params.topicId,
            showGuide: false,
        };
    },
};
</script>
