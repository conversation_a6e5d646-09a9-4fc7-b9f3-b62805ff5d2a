<template>
    <div class="hubble-page__upload">
        <Upload
            drag
            class="hubble-page__upload-wrapper"
            @onUploadSuccess="uploadSuccess"
            v-loading="loading"
        >
            <div>
                <div class="hubble-page__upload-operate">
                    <i class="el-icon-ssq-shangchuanbendiwenjian"></i>
                    <p>将文档拖拽至此上传</p>
                    <!-- <span class="accept">目前仅支持PDF文档</span><br> -->
                    <el-button>选择文档</el-button>
                </div>
                <span class="hubble-page__upload-drag-text">释放鼠标完成上传</span>
            </div>
        </Upload>
    </div>
</template>

<script>
import { mapMutations, mapState } from 'vuex';
import Upload from '../components/upload/index.vue';
export default {
    components: {
        Upload,
    },
    data() {
        return {
            loading: false,
        };
    },
    computed: {
        ...mapState('hubble', ['currentPackage']),
    },
    methods: {
        ...mapMutations('hubble', ['togglePackageDialog', 'toggleSlider']),
        uploadSuccess({ topicId, fileName }) {
            this.loading = false;
            this.$emit('uploadSuccess', { topicId, fileName });
            this.$router.push(`/hubble/chat/${topicId}`);
        },
    },
    created() {
        this.toggleSlider(false);
    },
};
</script>

<style lang="scss">
.hubble-page__upload{
    width: 100%;
    height: 100%;
    padding: 40px;
    box-sizing: border-box;
    background: #f8f8f8;
    &-wrapper{
        width: 100%;
        height: 100%;
        .el-upload{
            width: 100%;
            height: 100%;
        }
        .el-upload-dragger{
            width: 100%;
            height: 100%;
            border: 3px dashed rgba(221,221,221,1);
            border-radius: 8px;
            position: relative;
            background: transparent;
            .hubble-page__upload-drag-text{
                color: #0C8AEE;
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                display: none;
            }
            &:hover, &.is-dragover{
                border-color: #0C8AEE;
            }
            &.is-dragover{
                .hubble-page__upload-operate{
                    display: none;
                }
                .hubble-page__upload-drag-text{
                    display: block;
                }
            }
        }
    }
    &-operate{
        color: #999;
        width: 150px;
        text-align: center;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        i{
            font-size: 56px;
            margin-bottom: 20px;
        }
        p{
            font-size: 16px;
            margin-bottom: 14px;
        }
        span.accept{
            color: #ddd;
            font-size: 12px;
        }
        .el-button{
            margin-top: 30px;
        }
    }
}
</style>
