<template>
    <div class="hubble-page">
        <Header @initPackage="initPackage"></Header>
        <div class="hubble-page__layout">
            <div class="hubble-page__layout-slider-box" ref="sliderBox" v-if="$route.meta.hasSlider && hasPackage">
                <TopicSlider ref="topicSlider" v-show="showSlider"></TopicSlider>
            </div>
            <div class="hubble-page__layout-wrapper">
                <router-view @uploadSuccess="uploadSuccess" :key="$route.params.topicId || ''"></router-view>
            </div>
        </div>
        <PackageDialog
            @handleNext="checkPackage"
            v-model="showPackageDialog"
            :currentPackage="currentPackage"
        ></PackageDialog>
        <RegisterFooter v-if="isPC" class="login-footer"></RegisterFooter>
    </div>
</template>
<script>
import { mapMutations, mapState } from 'vuex';
import Header from './components/header/index.vue';
import RegisterFooter from 'components/register_footer/RegisterFooter.vue';
import TopicSlider from './components/topicSlider/index.vue';
import PackageDialog from './components/package/index.vue';
import { isPC } from 'src/common/utils/device.js';
export default {
    components: {
        Header,
        TopicSlider,
        PackageDialog,
        RegisterFooter,
    },
    data() {
        return {
            hasPackage: false,
            isPC: isPC(),
        };
    },
    computed: {
        ...mapState('hubble', ['showPackageDialog', 'currentPackage', 'showSlider']),
    },
    watch: {
        showSlider(val) {
            if (val) {
                this.$refs.sliderBox.style.width = '200px';
            } else {
                this.$refs.sliderBox.style.width = '0px';
            }
        },
    },
    methods: {
        ...mapMutations('hubble', ['setPackage', 'togglePackageDialog']),
        initAccount() {
            return this.$http.post('/web/hubble/users/beta-plan-init');
        },
        initPackage() {
            return this.$http('web/hubble/users/overview-plan-detail').then(res => {
                this.setPackage(res.data);
                if (!res.data.planType) {
                    this.togglePackageDialog(true);
                } else {
                    this.hasPackage = true;
                }
            });
        },
        async checkPackage() {
            this.togglePackageDialog(false);
            await this.initPackage();
        },
        uploadSuccess({ topicId, fileName }) {
            this.$refs.topicSlider.uploadSuccess({ topicId, fileName });
        },
    },
    async created() {
        if (this.$route.meta.isAgentPage) {
            return;
        }
        await this.initAccount();
        !this.$route.meta.isSharePage && this.initPackage();
    },
};
</script>
<style lang="scss">
.hubble-page{
    min-width: 1200px;
    height: calc(100vh - 35px);
    display: flex;
    flex-direction: column;
    background: #f8f8f8;
    &__layout{
        flex: 1;
        display: flex;
        height: calc(100vh - 60px - 35px);
        overflow: hidden;
        &-slider{
            width: 200px;
            &-box{
                overflow-x: hidden;
                transition: width 0.5s ease;
                background: #fff;
                box-shadow: 0px 0px 8px 0px rgba(0,0,0,0.1);
                position: relative;
                z-index: 10;
            }
        }
        &-wrapper{
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
    }
    &__content{
        display: flex;
        flex: 1;
        height: calc(100vh - 60px - 35px);
        background: #f8f8f8;
        &-document{
            min-width: 600px;
            height: 100%;
            flex: 1 1 0%;
        }
        &-chat{
            width: 40%;
            min-width: 450px;
            box-shadow: 0px 0px 8px 0px rgba(0,0,0,0.1);
        }
    }
    .el-dialog{
        border-radius: 4px;
        overflow: hidden;
        &__header{
            padding: 0 30px;
            line-height: 50px;
            border-bottom: 1px solid #eee;
        }
        &__close{
            position: absolute;
            top: 16px;
            right: 20px;
        }
        &__title{
            font-weight: normal;
        }
    }
}
</style>
