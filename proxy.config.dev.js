const fs = require('fs');
const path = require('path');

const targetMap = {
/* 线上环境start */
    'line': 'https://ent.bestsign.cn',
    'line-pre': 'https://ent-pre.bestsign.cn',
    /* 线上环境end */

    /* 测试环境start */
    'ent': 'https://ent.bestsign.info', // 预发布
    'ent-pre': 'https://ent-pre.bestsign.info',
    'ent2-k8s': 'https://ent2-k8s.bestsign.info',
    'ent2-hwy': 'https://ent2-hwy.bestsign.info',
    'ent3-hwy': 'https://ent3-hwy.bestsign.info',
    'ent2-k8s-hwy': 'https://ent2-k8s.bestsign.info',
    'ent3-k8s-hwy': 'https://ent3-k8s.bestsign.info',
    'ent4-hwy': 'https://ent4-hwy.bestsign.info',
    'ent5-hwy': 'https://ent5-hwy.bestsign.info',
    'ent-ja': 'https://ent-jp.bestsign.info', // 日文版预发布
    'ent-uae': 'https://uae.bestsign.com', // 阿联酋版环境
    /* 测试环境end */

    /* 开发环境start */
    'dev1': 'https://ent.bestsign.tech',
    'dev2': 'https://ent2.bestsign.tech', // 华为云2
    'dev3': 'https://ent3.bestsign.tech', // 华为云03
    'dev-k8s': 'https://ent-k8s.bestsign.tech',
    'dev2-k8s': 'https://ent2-k8s.bestsign.tech',
    'dev3-k8s': 'https://ent3-k8s.bestsign.tech',
    'dev4-k8s': 'https://ent4-k8s.bestsign.tech',
    'dev-k8s-ja': 'https://ent-jp.bestsign.tech', // 日文版开发
    /* 开发环境end */

};

// 重要：修改dev环境，改变根目录.env.proxy.local 文件即可【没有的话，自己建一个】， 支持直接使用地址、 targetMap中的枚举key
function getTarget() {
    let target = targetMap['ent2-hwy']; // 默认值
    try {
        let localTarget = '';
        const filePath = path.join(__dirname, './.env.proxy.local');
        const stat = fs.statSync(filePath);
        if (stat.isFile(filePath)) {
            const txt = fs.readFileSync(filePath, 'utf-8');
            localTarget = (txt || '').trim();
        }
        if (localTarget) {
            target = /^http/.test(localTarget) ? localTarget : targetMap[localTarget];
        }
    } catch (e) {
        console.log(e);
    }
    if (!target) {
        throw Error('dev proxy target指定error');
    }
    console.log(`dev本地代理地址为${target}`);
    return target;
}

module.exports = [
    {
        context: [
            '/ents',
            '/file',
            '/basis',
            '/users',
            '/demo-api',
            '/auth-center',
            '/octopus-api',
            '/contract-api',
            '/template-api',
            '/authenticated',
            '/api/auth-center',
            '/contract-export',
            '/contract-center',
            '/contract-center-bearing',
            '/dwh/customerMgrData',
            '/yz-editor',
            '/contract-search',
            '/ad-api',
            '/front/log',
            '/web/hubble',
            '/web/document-compare',
            '/web/hubble-agent',
        ],
        target: getTarget(),
        changeOrigin: true,
        secure: false,
        logLevel: 'silent',
    },
    {
        context: '/www',
        changeOrigin: true,
        // target: 'http://10.128.47.100:8137',
        target: 'https://www.bestsign.info',
        logLevel: 'silent', // debug模式可以确认代理结果
    },
    {
        context: '/yz-editor',
        changeOrigin: true,
        // target: 'http://119.3.64.65:8080', // 永中编辑器wo服务，测试
        target: 'http://10.128.8.213:8080', // 永中编辑器wo服务, 预发
        logLevel: 'silent',
        pathRewrite: {
            '^/yz-editor': '',
        },
    },
    {
        context: '/yz-dcs',
        changeOrigin: true,
        // target: 'http://122.112.203.221:8080', // 永中编辑器DCS服务，测试
        target: 'http://10.128.8.213:9380', // 永中编辑器wo服务，预发
        logLevel: 'silent',
        pathRewrite: {
            '^/yz-dcs': '',
        },
    },
];
